'use client';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import FormCreateAsic from '../../_components/Form/FormCreateAsic';
import { IContract } from '@/apis/contracts/contracts.type';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import {
    useDetailContract,
    useUpdateContract,
} from '@/apis/contracts/contracts.api';
import { ROUTES } from '@/lib/routes';

const UpdateContract = () => {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { mutate: updateContract } = useUpdateContract({
        onSuccess: () => {
            toast.success('Thêm hợp đồng thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.CONTRACTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const { data: contactDetail } = useDetailContract(id);

    const handleClose = () => {
        router.back();
    };
    const handleNewVersion = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        updateContract({ payload: payload as IContract, isMakeACopy: true });
    };
    const handleReplace = (data: IContract) => {
        const payload = convertFormValueToPayload(data, [
            'deliveryType',
            'regionDeliveryType',
            'paymentType',
            'valuePercent',
            'daysAfterSign',
            'paymentDocumentType',
            'paymentDocumentCount',
            'documentIncludedType',
            'documentQuantity',
            'deliveryWeek',
        ]);
        updateContract({ payload: payload as IContract, isMakeACopy: false });
    };
    return (
        <FormCreateAsic
            onNewVersion={handleNewVersion}
            onReplace={handleReplace}
            onClose={handleClose}
            initValue={
                convertPayloadToFormValue(contactDetail?.data) as IContract
            }
        />
    );
};

export default UpdateContract;
