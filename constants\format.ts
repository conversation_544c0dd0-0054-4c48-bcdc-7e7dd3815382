export const DateTimeFormat = 'DD/MM/YYYY';

/**
 * Format số tiền theo định dạng VND
 * @param amount - Số tiền cần format
 * @param showCurrency - <PERSON><PERSON> hiển thị ký hiệu tiền tệ hay không (mặc định: true)
 * @returns Chuỗi đã được format theo VND
 */
export const formatVND = (amount: number, showCurrency: boolean = true): string => {
    if (isNaN(amount) || amount === null || amount === undefined) {
        return showCurrency ? '0 VND' : '0';
    }

    const formatted = new Intl.NumberFormat('vi-VN').format(amount);
    return showCurrency ? `${formatted} VND` : formatted;
};

/**
 * Format số tiền theo định dạng USD
 * @param amount - Số tiền cần format
 * @param showCurrency - Có hiển thị ký hiệu tiền tệ hay không (mặc định: true)
 * @returns Chuỗi đã được format theo USD
 */
export const formatUSD = (amount: number, showCurrency: boolean = true): string => {
    if (isNaN(amount) || amount === null || amount === undefined) {
        return showCurrency ? '$0' : '0';
    }

    const formatted = new Intl.NumberFormat('en-US').format(amount);
    return showCurrency ? `$${formatted}` : formatted;
};

/**
 * Format số tiền với tùy chọn loại tiền tệ
 * @param amount - Số tiền cần format
 * @param currency - Loại tiền tệ ('VND' | 'USD')
 * @param showCurrency - Có hiển thị ký hiệu tiền tệ hay không (mặc định: true)
 * @returns Chuỗi đã được format
 */
export const formatCurrency = (
    amount: number,
    currency: 'VND' | 'USD' = 'VND',
    showCurrency: boolean = true
): string => {
    switch (currency) {
        case 'USD':
            return formatUSD(amount, showCurrency);
        case 'VND':
        default:
            return formatVND(amount, showCurrency);
    }
};
