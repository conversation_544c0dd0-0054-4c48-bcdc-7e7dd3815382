/**
 * Demo file để minh họa cách sử dụng các hàm format tiền tệ
 * File này có thể được xóa sau khi đã hiểu cách sử dụng
 */

import { formatVND, formatUSD, formatCurrency } from '@/constants/format';

// Ví dụ sử dụng hàm formatVND
console.log('=== Ví dụ formatVND ===');
console.log(formatVND(1000000)); // "1.000.000 VND"
console.log(formatVND(1000000, false)); // "1.000.000"
console.log(formatVND(1234567.89)); // "1.234.568 VND" (làm tròn)
console.log(formatVND(0)); // "0 VND"
console.log(formatVND(NaN)); // "0 VND"

// Ví dụ sử dụng hàm formatUSD
console.log('\n=== Ví dụ formatUSD ===');
console.log(formatUSD(1000)); // "$1,000"
console.log(formatUSD(1000, false)); // "1,000"
console.log(formatUSD(1234.56)); // "$1,235" (làm tròn)
console.log(formatUSD(0)); // "$0"
console.log(formatUSD(NaN)); // "$0"

// Ví dụ sử dụng hàm formatCurrency
console.log('\n=== Ví dụ formatCurrency ===');
console.log(formatCurrency(1000000, 'VND')); // "1.000.000 VND"
console.log(formatCurrency(1000, 'USD')); // "$1,000"
console.log(formatCurrency(1000000)); // "1.000.000 VND" (mặc định VND)
console.log(formatCurrency(1000, 'USD', false)); // "1,000"

// Ví dụ trong React component
export const CurrencyExamples = () => {
    const priceVND = 1500000;
    const priceUSD = 65.5;
    
    return (
        <div>
            <h3>Ví dụ hiển thị giá</h3>
            <p>Giá VND: {formatVND(priceVND)}</p>
            <p>Giá USD: {formatUSD(priceUSD)}</p>
            <p>Giá chỉ số: {formatVND(priceVND, false)}</p>
            
            {/* Sử dụng trong table */}
            <table>
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>Giá VND</th>
                        <th>Giá USD</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Sản phẩm A</td>
                        <td>{formatVND(2500000)}</td>
                        <td>{formatUSD(108.5)}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

// Ví dụ xử lý dữ liệu từ API
export const processApiData = (products: any[]) => {
    return products.map(product => ({
        ...product,
        formattedPriceVND: formatVND(product.priceVND),
        formattedPriceUSD: formatUSD(product.priceUSD),
        displayPrice: formatCurrency(product.price, product.currency)
    }));
};

// Ví dụ validation và xử lý lỗi
export const safeFormatCurrency = (amount: any, currency: 'VND' | 'USD' = 'VND') => {
    // Kiểm tra và chuyển đổi dữ liệu
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount) || numAmount < 0) {
        return currency === 'VND' ? '0 VND' : '$0';
    }
    
    return formatCurrency(numAmount, currency);
};

console.log('\n=== Ví dụ safeFormatCurrency ===');
console.log(safeFormatCurrency('1000000', 'VND')); // "1.000.000 VND"
console.log(safeFormatCurrency('invalid', 'USD')); // "$0"
console.log(safeFormatCurrency(-100, 'VND')); // "0 VND"
