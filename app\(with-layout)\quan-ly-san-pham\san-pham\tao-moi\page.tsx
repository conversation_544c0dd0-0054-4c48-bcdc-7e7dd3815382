'use client';

import { IProduct } from '@/apis/product/product.type';
import FormProducts from '../_components/FormProducts';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'react-toastify';
import { useCreateProduct } from '@/apis/product/product.api';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { KEYS_TO_PRODUCT } from '@/constants/key-convert';

const CreatProducts = () => {
    const router = useRouter();
    const { mutate: createProduct } = useCreateProduct({
        onSuccess: () => {
            toast.success('Tạo mới sản phẩm thành công');
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = (data: IProduct) => {
        data.productOptions.map((item) => {
            item.installationType = Number(item.installationType);
            item.productOptionType = Number(item.productOptionType);
        });
        data.commonStatus = 1;

        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT);

        createProduct(payload as IProduct);
    };
    const handleClose = () => {
        router.back();
    };
    return (
        <FormProducts
            page='tao-moi'
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};

export default CreatProducts;
